using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Linq;
using System.Collections.Generic;

public class CharacterValuesParty : MonoBehaviour
{
    ConfigsHandler configsHandler;

    CharatersForPartyUIHandler cfUIH;
    PartyConfigs partyConfigs;

    public BattleCharacter character; // the character that this UI represents

    // Dictionary that controls the colors of the rarities
    private readonly Dictionary<string, Color> RarityColors = new();

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        cfUIH = GameObject.Find("PartyConfs").GetComponent<CharatersForPartyUIHandler>();
        partyConfigs = GameObject.Find("PartiesContainer").GetComponent<PartyConfigs>();

        // Add the listener to the button to insert the character to the party
        GetComponent<Button>().onClick.AddListener(InsertCharaterToTeam);

        // Initialize RarityColors dictionary
        RarityColors.Add("Comum", GeneralInfo.GetTierColor("TR2"));
        RarityColors.Add("Inferior", GeneralInfo.GetTierColor("TR5"));
        RarityColors.Add("Raro", GeneralInfo.GetTierColor("TR3"));
        RarityColors.Add("Épico", GeneralInfo.GetTierColor("TR1"));
        RarityColors.Add("Lendário", GeneralInfo.GetTierColor("TR6"));
        RarityColors.Add("Elementar", GeneralInfo.GetTierColor("TR4"));
    }

    void Update()
    {
        // gets the most updated value of the character
        if (character != null) character = configsHandler.GetCharacterByID(character.id);

        // if null destroy the object
        if (character == null) Destroy(gameObject);
        else // otherwise update the values
        {
            transform.GetChild(2).GetComponent<Image>().color = RarityColors[character.rarity];
            transform.GetChild(5).GetComponent<TextMeshProUGUI>().text = character.name;
            transform.GetChild(4).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.level.ToString();
            //transform.GetChild(2).GetComponent<TextMeshProUGUI>().text = "Atk: " + character.stats.GetAtk(character.level).ToString();
        }

        CheckIfInPartyAndDead();


    }

    void InsertCharaterToTeam()
    {
        int partyIndex = cfUIH.selectedParty; // gets the selected party

        int slotIndex = cfUIH.GetSelectedSlot(out bool isActive); // gets the selected slot and if is an active slot or stock slot

        PartyCharacters party = configsHandler.GetPartyCharacters(partyIndex); // gets the party

        if (party.stockCharacters[slotIndex] != null) return; // if the slot is already occupied return

        configsHandler.SetCharacterToParty(partyIndex, slotIndex, isActive, character); // sets the character to the party in the selected slot

        partyConfigs.RefreshPartyUI();

    }

    void CheckIfInPartyAndDead()
    {
        int partyIndex = cfUIH.selectedParty;

        if (character.IsDead)
        {
            transform.GetChild(10).gameObject.SetActive(true);
            transform.GetChild(10).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
            transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().text = "<color=white>DEAD</color>";
        }
        else
        {
            if (configsHandler.partyCharacters[partyIndex].activeCharacters.Contains(character) || configsHandler.partyCharacters[partyIndex].stockCharacters.Contains(character))
            {
                transform.GetChild(10).gameObject.SetActive(true);
                transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().color = Color.yellow;
                transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().text = "<color=yellow>IN PARTY</color>";
            }
            else transform.GetChild(10).gameObject.SetActive(false);
        }
    }


}
