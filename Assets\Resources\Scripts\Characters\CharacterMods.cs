public class CharacterMods
{
    int knowledge, luck, speed, precision, evasion, criticalChance; // The values of each character's mods

    public CharacterMods() // Default values
    {
        knowledge = UnityEngine.Random.Range(1, 101); // Random 1-100
        luck = UnityEngine.Random.Range(1, 101); // Random 1-100
        speed = UnityEngine.Random.Range(1, 101); // Random 1-100
        precision = UnityEngine.Random.Range(1, 101); // Random 1-100
        evasion = UnityEngine.Random.Range(1, 101); // Random 1-100
        criticalChance = UnityEngine.Random.Range(1, 101); // Random 1-100
    }
    // Get the value of each mod
    public int GetKnowledge() => knowledge;
    public int GetLuck() => luck;
    public int GetSpeed() => speed;
    public int GetPrecision() => precision;
    public int GetEvasion() => evasion;
    public int GetCriticalChance() => criticalChance;

    // Set the value of each mod
    public void SetKnowledge(int knowledge) => this.knowledge = knowledge;
    public void SetLuck(int luck) => this.luck = luck;
    public void SetSpeed(int speed) => this.speed = speed;
    public void SetPrecision(int precision) => this.precision = precision;
    public void SetEvasion(int evasion) => this.evasion = evasion;
    public void SetCriticalChance(int criticalChance) => this.criticalChance = criticalChance;
}
