# Keyword, Modifier, and Character Rarity Localization System Documentation

## Overview

The Keyword, Modifier, and Character Rarity Localization System provides centralized text and data management for the Unity game. The system manages UI text (keywords), game modifier data (modifiers), and character rarity tiers through specialized manager classes with read-only access patterns. The system consists of four main components:

1. **JsonDataModels.cs** - Data structure definitions for keywords, modifiers, and tiers
2. **KeywordManager.cs** - Singleton manager for keyword access only
3. **GeneralInfo.cs** - Singleton manager for modifier and tier data access
4. **LoadValues.cs** - Default file creation and loading
5. **ImportJsonFile.cs** - Import functionality for external data updates

## System Architecture

### Manager Class Separation

The system uses a clean separation of concerns with two specialized manager classes:

#### **KeywordManager.cs** - UI Text Management
- **Purpose**: Handles only keyword/UI text localization
- **Data Source**: `generalInfo.json` → `keywordPkg` array
- **Access Pattern**: `KeywordManager.GetWord("GENERIC_ACCEPT")`
- **Responsibility**: UI text strings, button labels, menu items

#### **GeneralInfo.cs** - Game Data Management
- **Purpose**: Handles modifier and character rarity tier data
- **Data Source**: `generalInfo.json` → `modifierListPkg` and `tierListPkg` arrays
- **Access Pattern**: `GeneralInfo.GetModifierSkill("MO0")`, `GeneralInfo.GetTierName("TR1")`
- **Responsibility**: Game mechanics data, character stats, rarity information

### Benefits of Separation
- **Clear Responsibilities**: Each manager has a specific purpose
- **Maintainability**: Easier to extend and modify individual systems
- **Performance**: Optimized loading and lookup for each data type
- **Backward Compatibility**: Existing keyword code remains unchanged

### Data Structure

Keywords, modifiers, and character rarity tiers are stored in `generalInfo.json` with the following structure:

```json
{
  "keywordPkg": [
    {
      "id": "cw100",
      "key": "GENERIC_ACCEPT",
      "word": "Confirmar",
      "keywordTags": ["KWT4"]
    }
  ],
  "modifierListPkg": [
    {
      "id": "MO0",
      "skill": "Inteligência",
      "description": "Influencia a precisão e a evasão do personagem...",
      "acronym": "Qi"
    }
  ],
  "tierListPkg": [
    {
      "id": "TR1",
      "name": "Épico",
      "variance": "Character Rarity",
      "color": "#7030a0",
      "acronym": "Epi",
      "selectDrop": "Character Rarity"
    }
  ]
}
```

**Keyword Field Descriptions:**
- `id`: Unique identifier for the keyword (e.g., "cw100")
- `key`: Code reference constant used in scripts (e.g., "GENERIC_ACCEPT")
- `word`: Display text shown to users (e.g., "Confirmar")
- `keywordTags`: Array of category tags for organization (e.g., ["KWT4"])

**Modifier Field Descriptions:**
- `id`: Unique identifier for the modifier (e.g., "MO0")
- `skill`: Skill name displayed to users (e.g., "Inteligência")
- `description`: Detailed description of the modifier's effect
- `acronym`: Short abbreviation for UI display (e.g., "Qi")

**Tier Field Descriptions:**
- `id`: Unique identifier for the tier (e.g., "TR1")
- `name`: Display name for the rarity tier (e.g., "Épico")
- `variance`: Category type (e.g., "Character Rarity")
- `color`: Hex color code for UI theming (e.g., "#7030a0") - accessed via `GetTierColor()` which returns a parsed Unity Color object
- `acronym`: Short abbreviation for compact display (e.g., "Epi")
- `selectDrop`: Dropdown category for UI organization (e.g., "Character Rarity")

### File Location

The `generalInfo.json` file is stored in the same directory as other save files:
- **Windows**: `%APPDATA%\DKGRPGPrototype_unity\generalInfo.json`
- **Other Platforms**: `Application.persistentDataPath/generalInfo.json`

## Usage Guide

### Basic Usage in Code

#### Keyword Usage
```csharp
// Get a localized word
string acceptText = KeywordManager.GetWord("GENERIC_ACCEPT"); // Returns "Confirmar"

// Get word with fallback
string text = KeywordManager.GetWordOrDefault("MISSING_KEY", "Default Text");

// Check if key exists
if (KeywordManager.HasKey("GENERIC_ACCEPT"))
{
    // Key exists, safe to use
}

// Get complete keyword object for advanced usage
JsonKeyword keyword = KeywordManager.GetKeyword("GENERIC_ACCEPT");
if (keyword != null)
{
    Debug.Log($"ID: {keyword.id}, Word: {keyword.word}, Tags: {string.Join(", ", keyword.keywordTags)}");
}
```

#### Modifier Usage
```csharp
// Get modifier skill names
string knowledgeSkill = GeneralInfo.GetModifierSkill("MO0"); // Returns "Inteligência"
string luckSkill = GeneralInfo.GetModifierSkill("MO1"); // Returns "Sorte"

// Get modifier descriptions
string knowledgeDesc = GeneralInfo.GetModifierDescription("MO0");

// Get modifier acronyms
string knowledgeAcronym = GeneralInfo.GetModifierAcronym("MO0"); // Returns "Qi"

// Convenience methods for the 6 core modifiers - Skill Names
string knowledgeName = GeneralInfo.GetKnowledgeSkillName(); // Returns "Inteligência"
string luckName = GeneralInfo.GetLuckSkillName(); // Returns "Sorte"
string speedName = GeneralInfo.GetSpeedSkillName(); // Returns "Velocidade"
string precisionName = GeneralInfo.GetPrecisionSkillName(); // Returns "Precisão"
string evasionName = GeneralInfo.GetEvasionSkillName(); // Returns "Evasão"
string criticalName = GeneralInfo.GetCriticalChanceSkillName(); // Returns "Chance Crítica"

// Convenience methods for the 6 core modifiers - Acronyms
string knowledgeAcronym = GeneralInfo.GetKnowledgeAcronym(); // Returns "Qi"
string luckAcronym = GeneralInfo.GetLuckAcronym(); // Returns "So"
string speedAcronym = GeneralInfo.GetSpeedAcronym(); // Returns "Ve"
string precisionAcronym = GeneralInfo.GetPrecisionAcronym(); // Returns "Pr"
string evasionAcronym = GeneralInfo.GetEvasionAcronym(); // Returns "Ev"
string criticalAcronym = GeneralInfo.GetCriticalChanceAcronym(); // Returns "Cc"

// Convenience methods for the 6 core modifiers - Descriptions
string knowledgeDesc = GeneralInfo.GetKnowledgeDescription(); // Returns full description
string luckDesc = GeneralInfo.GetLuckDescription(); // Returns full description
string speedDesc = GeneralInfo.GetSpeedDescription(); // Returns full description
string precisionDesc = GeneralInfo.GetPrecisionDescription(); // Returns full description
string evasionDesc = GeneralInfo.GetEvasionDescription(); // Returns full description
string criticalDesc = GeneralInfo.GetCriticalChanceDescription(); // Returns full description

// Get complete modifier object for advanced usage
JsonModifier modifier = GeneralInfo.GetModifier("MO0");
if (modifier != null)
{
    Debug.Log($"ID: {modifier.id}, Skill: {modifier.skill}, Acronym: {modifier.acronym}");
    Debug.Log($"Description: {modifier.description}");
}
```

#### Character Rarity Tier Usage
```csharp
// Get tier display names
string epicTier = GeneralInfo.GetTierName("TR4"); // Returns "Épico"
string legendaryTier = GeneralInfo.GetTierName("TR5"); // Returns "Lendário"

// Get tier colors for UI theming (returns Unity Color objects)
Color epicColor = GeneralInfo.GetTierColor("TR4"); // Returns parsed Color from "#7030a0"
Color legendaryColor = GeneralInfo.GetTierColor("TR5"); // Returns parsed Color from "#ff8000"

// Get tier acronyms for compact display
string epicAcronym = GeneralInfo.GetTierAcronym("TR4"); // Returns "Epi"
string legendaryAcronym = GeneralInfo.GetTierAcronym("TR5"); // Returns "Leg"

// Get tier variance and selectDrop for categorization
string variance = GeneralInfo.GetTierVariance("TR4"); // Returns "Character Rarity"
string selectDrop = GeneralInfo.GetTierSelectDrop("TR4"); // Returns "Character Rarity"

// Get complete tier object for advanced usage
JsonTier tier = GeneralInfo.GetTier("TR4");
if (tier != null)
{
    Color tierColor = GeneralInfo.GetTierColor(tier.id); // Get parsed Color object
    Debug.Log($"ID: {tier.id}, Name: {tier.name}, Color: {tier.color} (Parsed: {tierColor})");
    Debug.Log($"Acronym: {tier.acronym}, Variance: {tier.variance}");
}
```

### UI Integration Examples

#### Keyword Integration Example

```csharp
// In a UI script
public class MenuButton : MonoBehaviour
{
    public TextMeshProUGUI buttonText;
    public string keywordKey = "GENERIC_ACCEPT";

    void Start()
    {
        // Set text from keyword system
        buttonText.text = KeywordManager.GetWord(keywordKey);
    }
}
```

#### Modifier Integration Example
```csharp
// In a character stats UI script
public class CharacterStatsUI : MonoBehaviour
{
    [Header("Modifier UI Elements")]
    public TextMeshProUGUI knowledgeLabel;
    public TextMeshProUGUI knowledgeAcronym;
    public TextMeshProUGUI knowledgeValue;
    public Button knowledgeInfoButton;

    [Header("Tooltip")]
    public GameObject tooltipPanel;
    public TextMeshProUGUI tooltipText;

    void Start()
    {
        // Set modifier labels using convenience methods
        knowledgeLabel.text = KeywordManager.GetKnowledgeSkillName(); // "Inteligência"
        knowledgeAcronym.text = KeywordManager.GetKnowledgeAcronym(); // "Qi"

        // Setup info button to show description
        knowledgeInfoButton.onClick.AddListener(() => ShowModifierTooltip("MO0"));
    }

    void ShowModifierTooltip(string modifierId)
    {
        string description = KeywordManager.GetModifierDescription(modifierId);
        tooltipText.text = description;
        tooltipPanel.SetActive(true);
    }

    void UpdateCharacterStats(CharacterData character)
    {
        // Display character's knowledge value
        knowledgeValue.text = character.knowledgeValue.ToString();
    }
}
```

#### Complete Modifier Display Example
```csharp
// In a modifier overview UI script
public class ModifierOverviewUI : MonoBehaviour
{
    [System.Serializable]
    public class ModifierUIElement
    {
        public string modifierId;
        public TextMeshProUGUI skillNameText;
        public TextMeshProUGUI acronymText;
        public TextMeshProUGUI descriptionText;
        public TextMeshProUGUI valueText;
    }

    [Header("Modifier UI Elements")]
    public ModifierUIElement[] modifierElements;

    void Start()
    {
        SetupModifierUI();
    }

    void SetupModifierUI()
    {
        foreach (var element in modifierElements)
        {
            // Use general methods for any modifier ID
            element.skillNameText.text = KeywordManager.GetModifierSkill(element.modifierId);
            element.acronymText.text = KeywordManager.GetModifierAcronym(element.modifierId);
            element.descriptionText.text = KeywordManager.GetModifierDescription(element.modifierId);
        }
    }

    void UpdateModifierValues(CharacterData character)
    {
        // Update values from character data
        modifierElements[0].valueText.text = character.knowledgeValue.ToString(); // MO0
        modifierElements[1].valueText.text = character.luckValue.ToString(); // MO1
        modifierElements[2].valueText.text = character.speedValue.ToString(); // MO2
        // ... etc
    }
}
```

#### Character Rarity UI Integration Example
```csharp
// Character rarity display with automatic color parsing
public class CharacterRarityDisplay : MonoBehaviour
{
    [Header("UI Elements")]
    public TextMeshProUGUI characterName;
    public TextMeshProUGUI rarityName;
    public TextMeshProUGUI rarityAcronym;
    public Image rarityBackground;
    public Image rarityBorder;

    public void SetCharacterRarity(string characterName, string tierId)
    {
        // Set character name
        this.characterName.text = characterName;

        // Get tier information using GeneralInfo
        string tierName = GeneralInfo.GetTierName(tierId);     // e.g., "Épico"
        string tierAcronym = GeneralInfo.GetTierAcronym(tierId); // e.g., "Epi"
        Color tierColor = GeneralInfo.GetTierColor(tierId);     // Parsed Color object

        // Apply tier information to UI
        rarityName.text = tierName;
        rarityAcronym.text = tierAcronym;

        // Apply color directly (no manual parsing needed!)
        rarityBackground.color = tierColor;
        rarityBorder.color = tierColor;

        // Optional: Adjust text color based on tier color brightness
        float brightness = (tierColor.r + tierColor.g + tierColor.b) / 3f;
        rarityName.color = brightness > 0.5f ? Color.black : Color.white;
    }

    // Example usage for different rarity tiers
    void Start()
    {
        // Examples of different tier applications
        SetCharacterRarity("Hero Alpha", "TR1");   // Common - Gray
        SetCharacterRarity("Hero Beta", "TR4");    // Epic - Purple
        SetCharacterRarity("Hero Gamma", "TR5");   // Legendary - Orange
    }
}
```

### Debugging and Inspection

```csharp
// Get all available keys
string[] allKeys = KeywordManager.GetAllKeys();
Debug.Log($"Available keywords: {string.Join(", ", allKeys)}");

// Get keyword count
int count = KeywordManager.GetKeywordCount();
Debug.Log($"Total keywords loaded: {count}");

// Check loading status
bool isLoaded = KeywordManager.Instance.IsLoaded();
Debug.Log($"Keywords loaded: {isLoaded}");

// Debug print all keywords (also available as context menu in inspector)
KeywordManager.Instance.DebugPrintAllKeywords();
```

## Default Keywords

The system comes with 20 pre-configured keywords covering common UI elements:

### Generic Actions (KWT4)
- `GENERIC_ACCEPT` → "Confirmar"
- `GENERIC_CANCEL` → "Cancelar"
- `GENERIC_OK` → "OK"
- `GENERIC_YES` → "Sim"
- `GENERIC_NO` → "Não"
- `GENERIC_BACK` → "Voltar"
- `GENERIC_NEXT` → "Próximo"
- `GENERIC_PREVIOUS` → "Anterior"
- `GENERIC_SAVE` → "Salvar"
- `GENERIC_LOAD` → "Carregar"
- `GENERIC_DELETE` → "Excluir"
- `GENERIC_EDIT` → "Editar"
- `GENERIC_NEW` → "Novo"
- `GENERIC_CLOSE` → "Fechar"
- `GENERIC_EXIT` → "Sair"

### Menu Items (KWT1)
- `MENU_MAIN` → "Menu Principal"
- `MENU_SETTINGS` → "Configurações"
- `MENU_CHARACTERS` → "Personagens"
- `MENU_BATTLE` → "Batalha"
- `MENU_INVENTORY` → "Inventário"

## Default Modifiers

The system comes with 6 pre-configured modifiers covering the core game mechanics:

### Core Modifiers
- **Knowledge (MO0)**: "Inteligência" (Qi)
  - *Description*: "Influencia a precisão e a evasão do personagem, aumentando a capacidade de acertar ataques e evitar danos."

- **Luck (MO1)**: "Sorte" (So)
  - *Description*: "Afeta a chance de acertos críticos e a probabilidade de encontrar itens raros durante a exploração."

- **Speed (MO2)**: "Velocidade" (Ve)
  - *Description*: "Determina a ordem de ação em combate e a capacidade de realizar múltiplas ações por turno."

- **Precision (MO3)**: "Precisão" (Pr)
  - *Description*: "Aumenta a chance de acertar ataques físicos e mágicos, reduzindo a probabilidade de errar o alvo."

- **Evasion (MO4)**: "Evasão" (Ev)
  - *Description*: "Melhora a capacidade de esquivar-se de ataques inimigos, reduzindo o dano recebido em combate."

- **Critical Chance (MO5)**: "Chance Crítica" (Cc)
  - *Description*: "Aumenta a probabilidade de causar dano crítico em ataques, multiplicando o dano causado."

## Import System

### Supported Import Formats

The import system can process JSON files containing keyword and/or modifier data in the following format:

```json
{
  "keywordPkg": {
    "exportedDateTime": "31/07/2025_12:43:52",
    "appVersion": "9.10.54",
    "data": [
      {
        "id": "cw200",
        "key": "NEW_KEYWORD",
        "word": "Nova Palavra",
        "keywordTags": ["KWT5"],
        "revisionCounterWordAI": 10,
        "extraField": "ignored"
      }
    ]
  },
  "modifierListPkg": {
    "exportedDateTime": "31/07/2025_12:43:52",
    "appVersion": "9.10.54",
    "data": [
      {
        "id": "MO6",
        "skill": "Nova Habilidade",
        "description": "Descrição da nova habilidade...",
        "acronym": "Nh",
        "revisionCounterSkillAI": 5,
        "revisionCounterDescriptionAI": 3,
        "isReviewedDescription": true
      }
    ]
  }
}
```

**Important Notes:**
- Keywords are nested in `keywordPkg.data` array, modifiers in `modifierListPkg.data` array
- Metadata fields (`exportedDateTime`, `appVersion`) are logged but not stored
- **Keywords**: Only four core fields (`id`, `key`, `word`, `keywordTags`) are imported
- **Modifiers**: Only four core fields (`id`, `skill`, `description`, `acronym`) are imported
- Extra fields like `revisionCounterWordAI`, `revisionCounterSkillAI`, `isReviewedDescription` are ignored for data integrity
- Existing keywords are updated based on matching `id` or `key`
- Existing modifiers are updated based on matching `id`
- New keywords/modifiers are added if no match is found
- Files can contain keywords only, modifiers only, or both

**Structure Explanation:**
- `keywordPkg`: Root container object for keywords
  - `exportedDateTime`: Export timestamp (metadata only)
  - `appVersion`: Source application version (metadata only)
  - `data`: Array containing the actual keyword objects
    - Each keyword object contains `id`, `key`, `word`, `keywordTags` plus any extra fields
- `modifierListPkg`: Root container object for modifiers
  - `exportedDateTime`: Export timestamp (metadata only)
  - `appVersion`: Source application version (metadata only)
  - `data`: Array containing the actual modifier objects
    - Each modifier object contains `id`, `skill`, `description`, `acronym` plus any extra fields

### Import Process

1. **File Selection**: Use the import button in the game to select a JSON file
2. **Automatic Detection**: The system automatically detects if the file contains keyword data
3. **Validation**: Only valid keywords with `id` and `key` fields are processed
4. **Update/Insert**: Existing keywords are updated, new ones are added
5. **Reload**: KeywordManager automatically reloads after import
6. **Logging**: Detailed logs show import results

### Import Logging

```
[KEYWORD_IMPORT] ✅ Keyword import completed: 5 new, 3 updated
[KEYWORD_IMPORT] ⚠️ Skipping invalid keyword with id='' key='INVALID'
[KEYWORD_IMPORT] ℹ️ No keyword data found in import file
```

## Adding New Keywords

### Method 1: Direct File Editing (Development Only)

Edit `generalInfo.json` directly during development:

```json
{
  "keywordPkg": [
    {
      "id": "cw120",
      "key": "BATTLE_ATTACK",
      "word": "Atacar",
      "keywordTags": ["KWT2", "BATTLE"]
    }
  ]
}
```

### Method 2: Import System (Recommended)

1. Create a JSON file with new keywords
2. Use the in-game import function
3. System automatically merges with existing keywords

### Method 3: Code Addition (Advanced)

Modify `CreateDefaultGeneralInfo()` in `LoadValues.cs` to add permanent keywords:

```csharp
new JsonKeyword("cw120", "BATTLE_ATTACK", "Atacar", new[] { "KWT2", "BATTLE" })
```

## Keyword Tags System

Tags provide categorization and organization:

- **KWT1**: Menu and navigation items
- **KWT2**: Battle and combat related
- **KWT3**: Character and stats related
- **KWT4**: Generic UI actions
- **KWT5**: Settings and configuration
- **CUSTOM**: Custom categories as needed

Tags can be used for:
- Filtering keywords by category
- Organizing translation workflows
- Grouping related functionality

## Error Handling

### Graceful Fallbacks

The system provides multiple levels of fallback:

1. **Missing Key**: Returns the key itself as display text
2. **Corrupted File**: Creates new default file with sample data
3. **Loading Failure**: Logs error but continues game execution
4. **Invalid Data**: Skips invalid entries, processes valid ones

### Common Issues and Solutions

**Issue**: Keywords not loading
- **Solution**: Check if `generalInfo.json` exists in save folder
- **Debug**: Use `KeywordManager.Instance.IsLoaded()` to check status

**Issue**: Import not working
- **Solution**: Verify JSON format and ensure `keywordPkg.data` array exists with proper nested structure
- **Debug**: Check console logs for detailed error messages

**Issue**: Keywords not updating after import
- **Solution**: KeywordManager automatically reloads, but you can force reload with `KeywordManager.ReloadKeywords()`

## Performance Considerations

- **Singleton Pattern**: KeywordManager uses singleton for efficient access
- **Dictionary Lookup**: O(1) keyword retrieval using dictionary
- **Lazy Loading**: Keywords loaded once on startup
- **Memory Efficient**: Minimal memory footprint for keyword storage

## Future Extensions

### Adding New Tables to generalInfo.json

To add new data tables (e.g., `settingsPkg`, `messagesPkg`):

1. **Update JsonGeneralInfo**: Add new array field
2. **Update LoadValues**: Modify `CreateDefaultGeneralInfo()` method
3. **Create Manager**: Create dedicated manager class following KeywordManager pattern
4. **Update Import**: Extend `ProcessKeywordImport()` for new data type

### Localization Support

The system is designed to support multiple languages:

1. **Language-specific files**: `generalInfo_en.json`, `generalInfo_pt.json`
2. **Language detection**: Extend KeywordManager to load appropriate file
3. **Runtime switching**: Add methods to change language dynamically

### Advanced Features

- **Keyword validation**: Ensure all used keys exist
- **Usage tracking**: Track which keywords are used where
- **Translation tools**: Export/import for translation workflows
- **Hot reloading**: Update keywords without game restart

## Integration Checklist

When integrating the keyword system into existing code:

- [ ] Replace hardcoded strings with `KeywordManager.GetWord()` calls
- [ ] Add keyword keys to `generalInfo.json` or import them
- [ ] Test fallback behavior for missing keys
- [ ] Update UI prefabs to use keyword system
- [ ] Document new keyword keys and their purposes
- [ ] Test import functionality with sample data
- [ ] Verify performance impact in target scenarios

## Conclusion

The Keyword Localization System provides a robust, scalable solution for text management in Unity games. It supports easy localization, centralized text updates, and maintains data integrity through controlled import processes. The system is designed to be developer-friendly while providing the flexibility needed for complex localization requirements.
