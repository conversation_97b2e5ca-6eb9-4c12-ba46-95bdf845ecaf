using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;

public class ImportJsonFile : MonoBehaviour
{
    public Button importButton;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        importButton.onClick.AddListener(OpenFileExplorer);
    }

    // Update is called once per frame
    void Update()
    {

    }

    void OpenFileExplorer()
    {
        NativeFilePicker.PickFile((importPath) =>
        {
            if (!string.IsNullOrEmpty(importPath))
            {
                // Determine target file based on import content
                string targetFile = DetermineTargetFile(importPath);

                if (!string.IsNullOrEmpty(targetFile))
                {
                    if (ImportFile(importPath, targetFile))
                    {
                        new ReloadGame(); //After successful import
                    }
                }
                else
                {
                    Debug.LogError("Unable to determine import target. File must contain either 'characterPkg' or 'keywordPkg' data.");
                }
            }
            else
            {
                Debug.LogWarning("No file selected.");
            }
        });
    }

    /// <summary>
    /// Determines the target file based on the content of the import file
    /// Returns "characters.json" if characterPkg data is found
    /// Returns "generalInfo.json" if keywordPkg data is found
    /// Returns null if neither is found
    /// </summary>
    /// <param name="importPath">Path to the import file</param>
    /// <returns>Target filename or null if undetermined</returns>
    string DetermineTargetFile(string importPath)
    {
        try
        {
            if (!File.Exists(importPath))
            {
                Debug.LogError($"Import file not found: {importPath}");
                return null;
            }

            var importJson = JObject.Parse(File.ReadAllText(importPath));

            // Check for character data
            bool hasCharacterData = importJson["characterPkg"]?["data"] is JArray charArray && charArray.Count > 0;

            // Check for keyword data - keywords are nested in keywordPkg.data
            bool hasKeywordData = importJson["keywordPkg"]?["data"] is JArray keywordArray && keywordArray.Count > 0;

            // Check for modifier data - modifiers are nested in modifierListPkg.data
            bool hasModifierData = importJson["modifierListPkg"]?["data"] is JArray modifierArray && modifierArray.Count > 0;

            // Check for tier data - tiers are nested in tierListPkg.data
            bool hasTierData = importJson["tierListPkg"]?["data"] is JArray tierArray && tierArray.Count > 0;

            if (hasCharacterData && (hasKeywordData || hasModifierData || hasTierData))
            {
                // If characters and general info data exist, prioritize characters but also process general info
                var generalDataTypes = new List<string>();
                if (hasKeywordData) generalDataTypes.Add("keyword");
                if (hasModifierData) generalDataTypes.Add("modifier");
                if (hasTierData) generalDataTypes.Add("tier");

                string dataTypes = $"character and {string.Join(", ", generalDataTypes)}";
                Debug.Log($"[IMPORT] 📦 Import file contains {dataTypes} data - processing both");
                return "characters.json";
            }
            else if (hasCharacterData)
            {
                Debug.Log("[IMPORT] 👥 Import file contains character data");
                return "characters.json";
            }
            else if (hasKeywordData || hasModifierData || hasTierData)
            {
                var dataTypes = new List<string>();
                if (hasKeywordData) dataTypes.Add("keyword");
                if (hasModifierData) dataTypes.Add("modifier");
                if (hasTierData) dataTypes.Add("tier");

                Debug.Log($"[IMPORT] 🔤 Import file contains {string.Join(", ", dataTypes)} data");
                return "generalInfo.json";
            }
            else
            {
                Debug.LogWarning("[IMPORT] ⚠️ Import file contains no recognizable data packages");
                return null;
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"[IMPORT] ❌ Failed to analyze import file: {ex.Message}");
            return null;
        }
    }

    bool ImportFile(string importPath, string targetJsonFile)
    {
        try
        {
            // Allow replacing characters.json and generalInfo.json
            if (!string.Equals(targetJsonFile, "characters.json", StringComparison.OrdinalIgnoreCase) &&
                !string.Equals(targetJsonFile, "generalInfo.json", StringComparison.OrdinalIgnoreCase))
            {
                Debug.LogError("Only characters.json and generalInfo.json can be replaced by import.");
                return false;
            }

            // Get the save folder path
            string saveFolder = (Application.platform == RuntimePlatform.WindowsEditor || Application.platform == RuntimePlatform.WindowsPlayer)
                ? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), @"DKGRPGPrototype_unity")
                : Application.persistentDataPath;

            string targetPath = Path.Combine(saveFolder, targetJsonFile);

            // Check if the import file exists
            if (!File.Exists(importPath))
            {
                Debug.LogError($"Import file not found: {importPath}");
                return false;
            }

            // Check if target file exists, create default structure if not
            JObject currentJson;
            if (!File.Exists(targetPath))
            {
                Debug.LogWarning($"Target file {targetJsonFile} not found. Creating default structure.");
                currentJson = new JObject
                {
                    ["characters"] = new JArray()
                };
            }
            else
            {
                currentJson = JObject.Parse(File.ReadAllText(targetPath));
            }

            var importJson = JObject.Parse(File.ReadAllText(importPath));

        var importedCharList = importJson["characterPkg"]?["data"] as JArray;
        var importedBattleUpList = importJson["battleUpgradePkg"]?["data"] as JArray;
        var importedStatsList = importJson["statusInfoPkg"]?["data"] as JArray;
        var importedModsList = importJson["primalModifierPkg"]?["data"] as JArray;
        var importedClassList = importJson["classPkg"]?["data"] as JArray;
        var importedAilmentsDefensesList = importJson["ailmentDefensesPkg"]?["data"] as JArray;
        var localList = currentJson["characters"] as JArray;

        if (importedCharList == null || localList == null)
        {
            Debug.LogError("Invalid JSON structure.");
            return false;
        }

        HashSet<string> allowedTypes = new HashSet<string> { "2", "3", "4" };

            foreach (var importedChar in importedCharList)
            {
                var typeStr = importedChar["type"]?.ToString();
                if (!allowedTypes.Contains(typeStr)) continue;

                var id = importedChar["id"]?.ToString();
                var name = importedChar["name"]?.ToString() ?? importedChar["character"]?.ToString();
                var rarity = importedChar["rarity"]?.ToString() ?? "Missing"; // Default fallback if missing
                if (string.IsNullOrEmpty(id)) continue;

                // Find matching objects in imported arrays by "character" == id
                var battleUp = importedBattleUpList?.FirstOrDefault(x => x["character"]?.ToString() == id);
                var stats = importedStatsList?.Where(x => x["character"]?.ToString() == id).ToList();

                // Find the primal modifier entry for this character and extract the primalModifier array
                var modsEntry = importedModsList?.FirstOrDefault(x => x["character"]?.ToString() == id);
                var mods = modsEntry?["primalModifier"] as JArray;

                var ailDefsEntry = importedAilmentsDefensesList?.FirstOrDefault(x => x["characterId"]?.ToString() == id);
                var ailDefs = ailDefsEntry?["ailmentDefensesList"] as JArray;

                // Find or create local character
                var localChar = localList.FirstOrDefault(c =>
                    c["id"] != null &&
                    (
                        (c["id"].Type == JTokenType.Integer && c["id"].ToString() == id) ||
                        (c["id"].Type == JTokenType.String && c["id"].ToString() == id)
                    )
                ) as JObject;

                if (localChar == null)
                {
                    localChar = new JObject();
                    localList.Add(localChar);
                }

                // Set id and name
                localChar["id"] = id;
                localChar["name"] = name;
                localChar["rarity"] = rarity;

                // Set isEnemy field (imported characters are player characters by default)
                localChar["isEnemy"] = false;

                // Set 'classe' from classId lookup
                var classId = importedChar["classId"]?.ToString();
                if (!string.IsNullOrEmpty(classId))
                {
                    var matchedClass = importedClassList?.FirstOrDefault(c => c["id"]?.ToString() == classId);
                    if (matchedClass != null)
                    {
                        localChar["classe"] = matchedClass["name"]?.ToString() ?? "";
                    }
                    else
                    {
                        localChar["classe"] = ""; // or set to "Unknown"
                        Debug.LogWarning($"Class ID {classId} not found in classPkg.");
                    }
                }
                else
                {
                    localChar["classe"] = ""; // or "Unassigned"
                    Debug.LogWarning($"Character {id} has no classId.");
                }

                // Level (bl)
                localChar["level"] = battleUp?["bl"] ?? 1;
                localChar["baseLevel"] = battleUp?["bl"] ?? 1;

                // Skills - Create fixed-size array indexed by Types enum values
                var skillsByType = new JArray();

                // Initialize all skill type slots with default values (8 types: Strength through Frost)
                int totalTypes = 8; // Types enum has 8 values: Strength, Magic, Fire, Venom, Possession, Electricity, Acid, Frost
                string[] typeNames = { "Strength", "Magic", "Fire", "Venom", "Possession", "Electricity", "Acid", "Frost" };
                string[] typeAcronyms = { "St", "Ma", "Fi", "Ve", "Po", "El", "Ac", "Fr" };

                for (int i = 0; i < totalTypes; i++)
                {
                    skillsByType.Add(new JObject
                    {
                        ["spDef"] = "0",
                        ["spAtk"] = "0",
                        ["type"] = typeNames[i],
                        ["acronym"] = typeAcronyms[i]
                    });
                }

                // Map imported stats to correct skill type positions based on acronym
                if (stats != null)
                {
                    foreach (var stat in stats)
                    {
                        string acronym = stat["acronym"]?.ToString() ?? "";
                        int typeIndex = GetTypeIndexFromAcronym(acronym);

                        if (typeIndex >= 0 && typeIndex < skillsByType.Count)
                        {
                            var skillSlot = skillsByType[typeIndex] as JObject;
                            if (skillSlot != null)
                            {
                                skillSlot["spDef"] = stat["spDef"]?.ToString() ?? "0";
                                skillSlot["spAtk"] = stat["spAtk"]?.ToString() ?? "0";
                                skillSlot["type"] = stat["type"]?.ToString() ?? "";
                                skillSlot["acronym"] = acronym;
                            }
                        }
                    }
                }

                localChar["skills"] = new JObject { ["skillsByType"] = skillsByType };

                // Stats - Create proper stat arrays or use imported data if available
                JArray apMinArray, hpArray, atkArray, defArray, atkLimArray, blArray;

                if (battleUp != null &&
                    battleUp["apMin"] is JArray importedApMin && importedApMin.Count > 0 &&
                    battleUp["hp"] is JArray importedHp && importedHp.Count > 0 &&
                    battleUp["atk"] is JArray importedAtk && importedAtk.Count > 0 &&
                    battleUp["def"] is JArray importedDef && importedDef.Count > 0 &&
                    battleUp["atkLim"] is JArray importedAtkLim && importedAtkLim.Count > 0)
                {
                    // Use imported data if all arrays are present and non-empty
                    apMinArray = importedApMin;
                    hpArray = importedHp;
                    atkArray = importedAtk;
                    defArray = importedDef;
                    atkLimArray = importedAtkLim;
                    blArray = battleUp["bl"] as JArray ?? GenerateDefaultBlArray(apMinArray.Count);
                }
                else
                {
                    // Generate default stat arrays (101 levels: 0-100)
                    apMinArray = GenerateDefaultApMinArray();
                    hpArray = GenerateDefaultHpArray();
                    atkArray = GenerateDefaultAtkArray();
                    defArray = GenerateDefaultDefArray();
                    atkLimArray = GenerateDefaultAtkLimArray(atkArray);
                    blArray = GenerateDefaultBlArray(101);
                }

                localChar["stats"] = new JObject
                {
                    ["apMin"] = apMinArray,
                    ["hp"] = hpArray,
                    ["atk"] = atkArray,
                    ["def"] = defArray,
                    ["atkLim"] = atkLimArray,
                    ["bl"] = blArray
                };

                // Mods
                JObject modsObj = new JObject();
                if (mods != null)
                {
                    modsObj["knowledge"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Inteligência")?["fieldValue"] ?? 0;
                    modsObj["luck"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Sorte")?["fieldValue"] ?? 0;
                    modsObj["speed"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Velocidade")?["fieldValue"] ?? 0;
                    modsObj["precision"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Precisão")?["fieldValue"] ?? 0;
                    modsObj["evasion"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Evasão")?["fieldValue"] ?? 0;
                    modsObj["criticalChance"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Chance Critica")?["fieldValue"] ?? 0;
                }
                localChar["mods"] = modsObj;

                // Ailment defenses
                JObject ailDefObj = new JObject();
                if (ailDefs != null)
                {
                    ailDefObj["charm"] = ailDefs.FirstOrDefault(a => a["fieldNameAilment"]?.ToString() == "Charme")?["fieldValueDefense"] ?? "";
                    ailDefObj["confusion"] = ailDefs.FirstOrDefault(a => a["fieldNameAilment"]?.ToString() == "Confusão")?["fieldValueDefense"] ?? "";
                    ailDefObj["curse"] = ailDefs.FirstOrDefault(a => a["fieldNameAilment"]?.ToString() == "Maldição")?["fieldValueDefense"] ?? "";
                    ailDefObj["paralysis"] = ailDefs.FirstOrDefault(a => a["fieldNameAilment"]?.ToString() == "Paralisia")?["fieldValueDefense"] ?? "";
                    ailDefObj["sleep"] = ailDefs.FirstOrDefault(a => a["fieldNameAilment"]?.ToString() == "Sono")?["fieldValueDefense"] ?? "";
                }
                localChar["ailDefs"] = ailDefObj;
            }

            // Process keyword and modifier imports if generalInfo.json is the target or if general info data exists in import
            ProcessGeneralInfoImport(importJson, saveFolder);

            // Save
            File.WriteAllText(targetPath, currentJson.ToString(Formatting.Indented));

            // Sync imported characters.json to chunked system
            var jsonList = JsonSaveHelper.LoadFromJson<JsonCharactersList>("characters.json");
            var allCharacters = JsonSaveHelper.ConvertCharactersFromJson(jsonList.characters);

            int totalChunks = (int)Math.Ceiling((double)allCharacters.Count / JsonSaveHelper.CHARACTERS_PER_CHUNK);
            for (int i = 0; i < totalChunks; i++)
            {
                JsonSaveHelper.SaveCharacterChunk(allCharacters, i);
            }

            //Clean up extra old chunks
            int oldChunkCount = JsonSaveHelper.GetChunkFileCount();
            for (int i = totalChunks; i < oldChunkCount; i++)
            {
                string extraChunkPath = Path.Combine(JsonSaveHelper.GetSaveFolder(), $"characters_chunk_{i}.json");
                if (File.Exists(extraChunkPath))
                    File.Delete(extraChunkPath);
            }

            Debug.Log("Characters merged and updated successfully.");
            return true;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error importing JSON file: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Maps skill acronyms to their corresponding Types enum index
    /// </summary>
    private int GetTypeIndexFromAcronym(string acronym)
    {
        return acronym?.ToUpper() switch
        {
            "ST" => 0, // Strength
            "MA" => 1, // Magic
            "FI" => 2, // Fire
            "VE" => 3, // Venom
            "PO" => 4, // Possession
            "EL" => 5, // Electricity
            "AC" => 6, // Acid
            "FR" => 7, // Frost
            _ => -1    // Unknown/invalid acronym
        };
    }

    /// <summary>
    /// Generates default apMin array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultApMinArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(i / 10); // apMin = level / 10
        }
        return array;
    }

    /// <summary>
    /// Generates default HP array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultHpArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(UnityEngine.Random.Range(50, 101) * (i + 1)); // hp = random(50-100) * (level + 1)
        }
        return array;
    }

    /// <summary>
    /// Generates default attack array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultAtkArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(UnityEngine.Random.Range(50, 101) * (i + 1)); // atk = random(50-100) * (level + 1)
        }
        return array;
    }

    /// <summary>
    /// Generates default defense array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultDefArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(UnityEngine.Random.Range(50, 101) * (i + 1)); // def = random(50-100) * (level + 1)
        }
        return array;
    }

    /// <summary>
    /// Generates default attack limit array based on attack values
    /// </summary>
    private JArray GenerateDefaultAtkLimArray(JArray atkArray)
    {
        var array = new JArray();
        foreach (var atk in atkArray)
        {
            array.Add((int)atk * 2); // atkLim = atk * 2
        }
        return array;
    }

    /// <summary>
    /// Generates default base level array
    /// </summary>
    private JArray GenerateDefaultBlArray(int count)
    {
        var array = new JArray();
        for (int i = 0; i < count; i++)
        {
            array.Add(i); // bl = level index
        }
        return array;
    }

    /// <summary>
    /// Processes keyword and modifier import from imported JSON file
    /// Updates generalInfo.json with keywords from keywordPkg and modifiers from modifierListPkg
    /// Only updates fields that exist in the generalInfo.json structure
    /// Keywords: (id, key, word, keywordTags) | Modifiers: (id, skill, description, acronym)
    /// </summary>
    /// <param name="importJson">The imported JSON object</param>
    /// <param name="saveFolder">The save folder path</param>
    private void ProcessGeneralInfoImport(JObject importJson, string saveFolder)
    {
        try
        {
            // Check for keyword, modifier, and tier data
            var keywordPkg = importJson["keywordPkg"];
            var modifierPkg = importJson["modifierListPkg"];
            var tierPkg = importJson["tierListPkg"];

            if (keywordPkg == null && modifierPkg == null && tierPkg == null)
            {
                Debug.Log("[GENERAL_INFO_IMPORT] ℹ️ No keywordPkg, modifierListPkg, or tierListPkg found in import file");
                return;
            }

            // Extract keyword data if available
            JArray importedKeywordList = null;
            if (keywordPkg != null)
            {
                importedKeywordList = keywordPkg["data"] as JArray;

                // Log keyword metadata if available
                var keywordDateTime = keywordPkg["exportedDateTime"]?.ToString();
                var keywordVersion = keywordPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(keywordDateTime) || !string.IsNullOrEmpty(keywordVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Keyword metadata - DateTime: {keywordDateTime ?? "N/A"}, Version: {keywordVersion ?? "N/A"}");
                }
            }

            // Extract modifier data if available
            JArray importedModifierList = null;
            if (modifierPkg != null)
            {
                importedModifierList = modifierPkg["data"] as JArray;

                // Log modifier metadata if available
                var modifierDateTime = modifierPkg["exportedDateTime"]?.ToString();
                var modifierVersion = modifierPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(modifierDateTime) || !string.IsNullOrEmpty(modifierVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Modifier metadata - DateTime: {modifierDateTime ?? "N/A"}, Version: {modifierVersion ?? "N/A"}");
                }
            }

            // Extract tier data if available
            JArray importedTierList = null;
            if (tierPkg != null)
            {
                importedTierList = tierPkg["data"] as JArray;

                // Log tier metadata if available
                var tierDateTime = tierPkg["exportedDateTime"]?.ToString();
                var tierVersion = tierPkg["appVersion"]?.ToString();
                if (!string.IsNullOrEmpty(tierDateTime) || !string.IsNullOrEmpty(tierVersion))
                {
                    Debug.Log($"[GENERAL_INFO_IMPORT] 📋 Tier metadata - DateTime: {tierDateTime ?? "N/A"}, Version: {tierVersion ?? "N/A"}");
                }
            }

            // Check if we have any data to process
            bool hasKeywords = importedKeywordList != null && importedKeywordList.Count > 0;
            bool hasModifiers = importedModifierList != null && importedModifierList.Count > 0;
            bool hasTiers = importedTierList != null && importedTierList.Count > 0;

            if (!hasKeywords && !hasModifiers && !hasTiers)
            {
                Debug.Log("[GENERAL_INFO_IMPORT] ℹ️ No keyword, modifier, or tier data found in import file");
                return;
            }

            string generalInfoPath = Path.Combine(saveFolder, "generalInfo.json");

            // Load existing generalInfo.json or create default structure
            JObject currentGeneralInfo;
            if (File.Exists(generalInfoPath))
            {
                currentGeneralInfo = JObject.Parse(File.ReadAllText(generalInfoPath));
            }
            else
            {
                Debug.LogWarning("[GENERAL_INFO_IMPORT] ⚠️ generalInfo.json not found. Creating default structure.");
                currentGeneralInfo = new JObject
                {
                    ["keywordPkg"] = new JArray(),
                    ["modifierListPkg"] = new JArray(),
                    ["tierListPkg"] = new JArray()
                };
            }

            // Ensure both arrays exist
            var localKeywordList = currentGeneralInfo["keywordPkg"] as JArray;
            if (localKeywordList == null)
            {
                localKeywordList = new JArray();
                currentGeneralInfo["keywordPkg"] = localKeywordList;
            }

            var localModifierList = currentGeneralInfo["modifierListPkg"] as JArray;
            if (localModifierList == null)
            {
                localModifierList = new JArray();
                currentGeneralInfo["modifierListPkg"] = localModifierList;
            }

            var localTierList = currentGeneralInfo["tierListPkg"] as JArray;
            if (localTierList == null)
            {
                localTierList = new JArray();
                currentGeneralInfo["tierListPkg"] = localTierList;
            }

            int keywordImported = 0;
            int keywordUpdated = 0;
            int modifierImported = 0;
            int modifierUpdated = 0;
            int tierImported = 0;
            int tierUpdated = 0;

            // Process keywords if available
            if (hasKeywords)
            {
                foreach (var importedKeyword in importedKeywordList)
                {
                    var id = importedKeyword["id"]?.ToString();
                    var key = importedKeyword["key"]?.ToString();
                    var word = importedKeyword["word"]?.ToString();
                    var keywordTags = importedKeyword["keywordTags"] as JArray;

                    // Log extra fields being ignored (for debugging)
                    if (importedKeyword is JObject keywordObj)
                    {
                        var extraFields = keywordObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "key" && p.Name != "word" && p.Name != "keywordTags")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra keyword fields for '{key}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid keywords
                    if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(key))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid keyword with id='{id}' key='{key}'");
                        continue;
                    }

                    // Find existing keyword by id or key
                    var existingKeyword = localKeywordList.FirstOrDefault(k =>
                        (k["id"]?.ToString() == id) || (k["key"]?.ToString() == key)
                    ) as JObject;

                    if (existingKeyword != null)
                    {
                        // Update existing keyword - only update allowed fields
                        existingKeyword["id"] = id;
                        existingKeyword["key"] = key;
                        existingKeyword["word"] = word ?? "";
                        if (keywordTags != null)
                        {
                            existingKeyword["keywordTags"] = keywordTags;
                        }
                        keywordUpdated++;
                    }
                    else
                    {
                        // Create new keyword - only include allowed fields
                        var newKeyword = new JObject
                        {
                            ["id"] = id,
                            ["key"] = key,
                            ["word"] = word ?? "",
                            ["keywordTags"] = keywordTags ?? new JArray()
                        };
                        localKeywordList.Add(newKeyword);
                        keywordImported++;
                    }
                }
            }

            // Process modifiers if available
            if (hasModifiers)
            {
                foreach (var importedModifier in importedModifierList)
                {
                    var id = importedModifier["id"]?.ToString();
                    var skill = importedModifier["skill"]?.ToString();
                    var description = importedModifier["description"]?.ToString();
                    var acronym = importedModifier["acronym"]?.ToString();

                    // Log extra fields being ignored (for debugging)
                    if (importedModifier is JObject modifierObj)
                    {
                        var extraFields = modifierObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "skill" && p.Name != "description" && p.Name != "acronym")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra modifier fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid modifiers
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid modifier with id='{id}'");
                        continue;
                    }

                    // Find existing modifier by id
                    var existingModifier = localModifierList.FirstOrDefault(m =>
                        m["id"]?.ToString() == id
                    ) as JObject;

                    if (existingModifier != null)
                    {
                        // Update existing modifier - only update allowed fields
                        existingModifier["id"] = id;
                        existingModifier["skill"] = skill ?? "";
                        existingModifier["description"] = description ?? "";
                        existingModifier["acronym"] = acronym ?? "";
                        modifierUpdated++;
                    }
                    else
                    {
                        // Create new modifier - only include allowed fields
                        var newModifier = new JObject
                        {
                            ["id"] = id,
                            ["skill"] = skill ?? "",
                            ["description"] = description ?? "",
                            ["acronym"] = acronym ?? ""
                        };
                        localModifierList.Add(newModifier);
                        modifierImported++;
                    }
                }
            }

            // Process tiers if available
            if (hasTiers)
            {
                foreach (var importedTier in importedTierList)
                {
                    var id = importedTier["id"]?.ToString();
                    var name = importedTier["name"]?.ToString();
                    var variance = importedTier["variance"]?.ToString();
                    var color = importedTier["color"]?.ToString();
                    var acronym = importedTier["acronym"]?.ToString();
                    var selectDrop = importedTier["selectDrop"]?.ToString();

                    // Log extra fields being ignored (for debugging)
                    if (importedTier is JObject tierObj)
                    {
                        var extraFields = tierObj.Properties()
                            .Where(p => p.Name != "id" && p.Name != "name" && p.Name != "variance" &&
                                       p.Name != "color" && p.Name != "acronym" && p.Name != "selectDrop")
                            .Select(p => p.Name)
                            .ToArray();

                        if (extraFields.Length > 0)
                        {
                            Debug.Log($"[GENERAL_INFO_IMPORT] 🔍 Ignoring extra tier fields for '{id}': {string.Join(", ", extraFields)}");
                        }
                    }

                    // Skip invalid tiers
                    if (string.IsNullOrEmpty(id))
                    {
                        Debug.LogWarning($"[GENERAL_INFO_IMPORT] ⚠️ Skipping invalid tier with id='{id}'");
                        continue;
                    }

                    // Find existing tier by id
                    var existingTier = localTierList.FirstOrDefault(t =>
                        t["id"]?.ToString() == id
                    ) as JObject;

                    if (existingTier != null)
                    {
                        // Update existing tier - only update allowed fields
                        existingTier["id"] = id;
                        existingTier["name"] = name ?? "";
                        existingTier["variance"] = variance ?? "";
                        existingTier["color"] = color ?? "";
                        existingTier["acronym"] = acronym ?? "";
                        existingTier["selectDrop"] = selectDrop ?? "";
                        tierUpdated++;
                    }
                    else
                    {
                        // Create new tier - only include allowed fields
                        var newTier = new JObject
                        {
                            ["id"] = id,
                            ["name"] = name ?? "",
                            ["variance"] = variance ?? "",
                            ["color"] = color ?? "",
                            ["acronym"] = acronym ?? "",
                            ["selectDrop"] = selectDrop ?? ""
                        };
                        localTierList.Add(newTier);
                        tierImported++;
                    }
                }
            }

            // Save updated generalInfo.json
            File.WriteAllText(generalInfoPath, currentGeneralInfo.ToString(Formatting.Indented));

            // Log results
            var results = new List<string>();
            if (hasKeywords)
                results.Add($"Keywords: {keywordImported} new, {keywordUpdated} updated");
            if (hasModifiers)
                results.Add($"Modifiers: {modifierImported} new, {modifierUpdated} updated");
            if (hasTiers)
                results.Add($"Tiers: {tierImported} new, {tierUpdated} updated");

            Debug.Log($"[GENERAL_INFO_IMPORT] ✅ Import completed - {string.Join(" | ", results)}");

            // Reload data in KeywordManager and GeneralInfo
            KeywordManager.ReloadKeywords();
            GeneralInfo.ReloadGeneralInfo();
        }
        catch (Exception ex)
        {
            Debug.LogError($"[GENERAL_INFO_IMPORT] ❌ Failed to process general info import: {ex.Message}");
        }
    }
}
